import bpy
from openai import OpenAI
import re
import os
import sys
import time
import json




def get_api_key(context, addon_name):
    preferences = context.preferences
    addon_prefs = preferences.addons[addon_name].preferences
    return addon_prefs.api_key


def init_props():
    bpy.types.Scene.blendpro_chat_history = bpy.props.CollectionProperty(type=bpy.types.PropertyGroup)
    bpy.types.Scene.blendpro_model = bpy.props.EnumProperty(
    name="GPT Model",
    description="Select the GPT model to use",
    items=[
        ("gpt-4", "GPT-4", "Use GPT-4"),
        ("gpt-4-turbo", "GPT-4 Turbo", "Use GPT-4 Turbo"),
        ("gpt-3.5-turbo", "GPT-3.5 Turbo", "Use GPT-3.5 Turbo"),
        ("gpt-4o", "GPT-4o", "Use GPT-4o"),
        ("gpt-4o-mini", "GPT-4o Mini", "Use GPT-4o Mini"),
    ],
    default="gpt-4",
)
    bpy.types.Scene.blendpro_vision_model = bpy.props.EnumProperty(
    name="Vision Model",
    description="Select the Vision model to use",
    items=[
        ("gpt-4-vision-preview", "GPT-4 Vision Preview", "Use GPT-4 Vision Preview"),
        ("gpt-4o", "GPT-4o", "Use GPT-4o (supports vision)"),
        ("gpt-4o-mini", "GPT-4o Mini", "Use GPT-4o Mini (supports vision)"),
        ("claude-3-5-sonnet-20241022", "Claude 3.5 Sonnet", "Use Claude 3.5 Sonnet (supports vision)"),
        ("claude-3-opus-20240229", "Claude 3 Opus", "Use Claude 3 Opus (supports vision)"),
        ("claude-3-sonnet-20240229", "Claude 3 Sonnet", "Use Claude 3 Sonnet (supports vision)"),
        ("claude-3-haiku-20240307", "Claude 3 Haiku", "Use Claude 3 Haiku (supports vision)"),
    ],
    default="gpt-4-vision-preview",
)
    bpy.types.Scene.blendpro_chat_input = bpy.props.StringProperty(
        name="Message",
        description="Enter your message",
        default="",
    )
    bpy.types.Scene.blendpro_button_pressed = bpy.props.BoolProperty(default=False)
    bpy.types.Scene.blendpro_monitoring_active = bpy.props.BoolProperty(default=False)
    bpy.types.Scene.blendpro_monitoring_interval = bpy.props.FloatProperty(
        name="Monitoring Interval",
        description="Interval between scene checks (seconds)",
        default=2.0,
        min=0.5,
        max=10.0
    )
    bpy.types.PropertyGroup.type = bpy.props.StringProperty()
    bpy.types.PropertyGroup.content = bpy.props.StringProperty()

def clear_props():
    del bpy.types.Scene.blendpro_chat_history
    del bpy.types.Scene.blendpro_chat_input
    del bpy.types.Scene.blendpro_button_pressed
    del bpy.types.Scene.blendpro_model
    del bpy.types.Scene.blendpro_vision_model
    del bpy.types.Scene.blendpro_monitoring_active
    del bpy.types.Scene.blendpro_monitoring_interval

def get_chat_history_file_path():
    """Get the file path for saving chat history"""
    user_data_dir = bpy.utils.user_resource('DATAFILES')
    if not os.path.exists(user_data_dir):
        os.makedirs(user_data_dir)
    return os.path.join(user_data_dir, "blendergpt_history.json")

def save_chat_history(chat_history):
    """Save chat history to JSON file"""
    try:
        file_path = get_chat_history_file_path()
        history_data = []

        for message in chat_history:
            history_data.append({
                "type": message.type,
                "content": message.content
            })

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(history_data, f, indent=2, ensure_ascii=False)

        print(f"Chat history saved to: {file_path}")
        return True
    except Exception as e:
        print(f"Error saving chat history: {e}")
        return False

def load_chat_history(context):
    """Load chat history from JSON file"""
    try:
        file_path = get_chat_history_file_path()
        if not os.path.exists(file_path):
            print("No saved chat history found")
            return False

        with open(file_path, 'r', encoding='utf-8') as f:
            history_data = json.load(f)

        # Clear existing history
        context.scene.blendpro_chat_history.clear()

        # Load saved history
        for item in history_data:
            message = context.scene.blendpro_chat_history.add()
            message.type = item.get("type", "user")
            message.content = item.get("content", "")

        print(f"Chat history loaded from: {file_path}")
        return True
    except Exception as e:
        print(f"Error loading chat history: {e}")
        return False

def save_scene_state():
    """Save current scene state for undo functionality"""
    try:
        import tempfile
        import datetime

        # Create a temporary blend file with timestamp
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        temp_dir = tempfile.gettempdir()
        backup_path = os.path.join(temp_dir, f"blendergpt_backup_{timestamp}.blend")

        # Save current scene
        bpy.ops.wm.save_as_mainfile(filepath=backup_path, copy=True)

        print(f"Scene state saved to: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"Error saving scene state: {e}")
        return None

def get_recent_backups(max_count=10):
    """Get list of recent backup files"""
    try:
        import tempfile
        import glob

        temp_dir = tempfile.gettempdir()
        pattern = os.path.join(temp_dir, "blendergpt_backup_*.blend")
        backup_files = glob.glob(pattern)

        # Sort by modification time (newest first)
        backup_files.sort(key=os.path.getmtime, reverse=True)

        return backup_files[:max_count]
    except Exception as e:
        print(f"Error getting backup files: {e}")
        return []

def cleanup_old_backups(keep_count=5):
    """Clean up old backup files, keeping only the most recent ones"""
    try:
        backups = get_recent_backups(100)  # Get more to clean up

        if len(backups) > keep_count:
            for backup in backups[keep_count:]:
                try:
                    os.remove(backup)
                    print(f"Removed old backup: {backup}")
                except Exception as e:
                    print(f"Error removing backup {backup}: {e}")
    except Exception as e:
        print(f"Error cleaning up backups: {e}")




def detect_context_level(prompt: str) -> int:
    """
    Detect required context level based on user prompt
    0: No context needed
    1: Basic scene data
    2: Detailed scene analysis
    3: Full vision analysis
    """
    prompt_lower = prompt.lower()

    # Level 3: Full vision analysis keywords
    vision_keywords = ['see', 'look', 'analyze', 'composition', 'visual', 'appearance', 'color', 'lighting setup', 'scene analysis']
    if any(keyword in prompt_lower for keyword in vision_keywords):
        return 3

    # Level 2: Detailed scene context keywords
    scene_keywords = ['current', 'scene', 'visible', 'this', 'these', 'objects', 'materials', 'lights', 'cameras']
    if any(keyword in prompt_lower for keyword in scene_keywords):
        return 2

    # Level 1: Basic context keywords
    basic_keywords = ['create', 'add', 'make', 'modify', 'change', 'update', 'delete', 'remove']
    if any(keyword in prompt_lower for keyword in basic_keywords):
        return 1

    # Level 0: No context needed
    return 0

def generate_smart_context(context, level: int, prompt: str) -> str:
    """Generate appropriate context based on detected level"""

    if level == 0:
        return ""

    try:
        # Import vision utilities if available
        from .vision_utilities import extract_comprehensive_scene_data, create_vision_context_for_base_ai

        if level == 1:
            # Basic context: just object and material counts
            scene_data = extract_comprehensive_scene_data(context)
            objects = scene_data.get("objects", [])
            materials = scene_data.get("materials", [])
            lights = scene_data.get("lights", [])

            basic_context = f"""
Current Scene Summary:
- Objects: {len(objects)} total
- Materials: {len(materials)} total
- Lights: {len(lights)} total
"""
            return basic_context

        elif level == 2:
            # Detailed context: full scene data
            scene_data = extract_comprehensive_scene_data(context)
            detailed_context = create_vision_context_for_base_ai({'scene_data': scene_data})
            return f"\n\nDetailed Scene Context:\n{detailed_context}"

        elif level == 3:
            # Full vision context: scene data + visual analysis
            from .vision_utilities import analyze_scene_with_vision

            # Get preferences for vision API
            preferences = context.preferences
            addon_prefs = preferences.addons[__name__].preferences

            # Get Vision API credentials
            vision_api_key = addon_prefs.vision_api_key or addon_prefs.api_key
            vision_base_url = addon_prefs.vision_base_url or addon_prefs.base_url
            vision_model = addon_prefs.custom_vision_model if addon_prefs.use_custom_vision_model else context.scene.blendpro_vision_model

            if vision_api_key:
                # Perform vision analysis
                vision_result = analyze_scene_with_vision(
                    context=context,
                    user_prompt=f"Analyze this scene for: {prompt}",
                    api_key=vision_api_key,
                    base_url=vision_base_url,
                    model=vision_model
                )

                if not vision_result.get("error"):
                    vision_context = create_vision_context_for_base_ai(vision_result)
                    return f"\n\nFull Vision Analysis Context:\n{vision_context}"

            # Fallback to level 2 if vision fails
            scene_data = extract_comprehensive_scene_data(context)
            fallback_context = create_vision_context_for_base_ai({'scene_data': scene_data})
            return f"\n\nScene Context (Vision unavailable):\n{fallback_context}"

    except Exception as e:
        print(f"Error generating smart context: {e}")
        return f"\n\nNote: Context generation failed: {str(e)}"

    return ""

def generate_blender_code(prompt, chat_history, context, system_prompt, api_key, base_url=None, model=None, timeout=60, temperature=0.7, max_tokens=1500, top_p=1.0):
    # Create OpenAI client with API key, optional base_url and timeout
    client_kwargs = {
        "api_key": api_key,
        "timeout": timeout  # Add timeout support
    }
    if base_url and base_url.strip():
        # Validate base_url format
        base_url = base_url.strip()
        if not (base_url.startswith('http://') or base_url.startswith('https://')):
            print(f"Warning: Base URL '{base_url}' should start with http:// or https://")
        client_kwargs["base_url"] = base_url

    try:
        client = OpenAI(**client_kwargs)
    except Exception as e:
        print(f"Error creating OpenAI client: {e}")
        return {"error": f"Client creation failed: {str(e)}", "code": None}

    # Detect required context level automatically
    context_level = detect_context_level(prompt)

    # Generate context based on detected level
    scene_context = ""
    if context_level > 0:
        try:
            scene_context = generate_smart_context(context, context_level, prompt)
        except Exception as e:
            print(f"Warning: Could not generate scene context: {e}")
            scene_context = "\n\nNote: Scene context unavailable."

    # Enhanced system prompt with context awareness
    enhanced_system_prompt = system_prompt
    if scene_context:
        enhanced_system_prompt += f"\n\nYou have access to current scene context (Level {context_level}). Use this information to make your code more specific to the current scene setup."

    messages = [{"role": "system", "content": enhanced_system_prompt}]
    for message in chat_history[-10:]:
        if message.type == "assistant":
            messages.append({"role": "assistant", "content": "```\n" + message.content + "\n```"})
        else:
            messages.append({"role": message.type.lower(), "content": message.content})

    # Add the current user message with optional scene context
    user_message = "Can you please write Blender code for me that accomplishes the following task: " + prompt + "? \n. Do not respond with anything that is not Python code. Do not provide explanations"

    if scene_context:
        user_message += scene_context

    messages.append({"role": "user", "content": user_message})

    # Determine which model to use
    selected_model = model if model and model.strip() else context.scene.blendpro_model
    print(f"Using model: {selected_model}")
    print(f"Base URL: {base_url if base_url else 'Default OpenAI'}")
    print(f"Messages count: {len(messages)}")

    try:
        print(f"Making API call with:")
        print(f"  Model: {selected_model}")
        print(f"  Base URL: {base_url}")
        print(f"  Messages count: {len(messages)}")
        print(f"  Last message: {messages[-1]['content'][:100]}...")

        response = client.chat.completions.create(
            model=selected_model,
            messages=messages,
            stream=True,
            max_tokens=max_tokens,
            temperature=temperature,
            top_p=top_p,
        )
        print(f"API call successful, got response object: {type(response)}")
    except Exception as e:
        error_msg = f"API call failed: {str(e)}"
        print(f"API call exception: {e}")
        print(f"Exception type: {type(e)}")

        # Provide more specific error messages
        if "model" in str(e).lower():
            error_msg = f"Model '{selected_model}' not available. Please check your model selection."
        elif "authentication" in str(e).lower() or "api key" in str(e).lower():
            error_msg = "Authentication failed. Please check your API key."
        elif "connection" in str(e).lower():
            error_msg = "Connection failed. Please check your internet connection and base URL."

        return {"error": error_msg, "code": None}

    try:
        collected_events = []
        completion_text = ''
        chunk_count = 0

        print(f"Starting to process streaming response...")

        # iterate through the stream of events
        for chunk in response:
            print(f"Received chunk: {chunk}")  # Debug: print raw chunk

            # Check if chunk has choices and delta
            if not hasattr(chunk, 'choices') or not chunk.choices:
                print(f"Chunk has no choices: {chunk}")
                continue

            choice = chunk.choices[0]
            print(f"Choice: {choice}")  # Debug: print choice

            if not hasattr(choice, 'delta'):
                print(f"Choice has no delta: {choice}")
                continue

            delta = choice.delta
            print(f"Delta: {delta}")  # Debug: print delta

            if hasattr(delta, 'role') and delta.role:
                print(f"Skipping role message: {delta.role}")
                # skip role messages
                continue
            if not hasattr(delta, 'content') or not delta.content:
                print(f"Delta has no content or empty content: {delta}")
                # skip empty content
                continue

            collected_events.append(chunk)  # save the event response
            event_text = delta.content
            completion_text += event_text  # append the text
            print(f"Added content: '{event_text}' (total length: {len(completion_text)})")

            # Print progress every 5 chunks to see more details
            chunk_count += 1
            if chunk_count % 5 == 0:
                print(f"Receiving response... ({len(completion_text)} characters)", flush=True)

            # Add small delay to prevent overwhelming the system
            time.sleep(0.001)  # 1ms delay

        print(f"Finished processing stream. Total chunks: {chunk_count}, Total text length: {len(completion_text)}")
        print(f"Final completion text: '{completion_text[:200]}...'")

        # Check if we received any content at all
        if not completion_text or len(completion_text.strip()) == 0:
            print("ERROR: No content received from API")
            return {"error": "Empty response from API - no content received in stream", "code": None}

        # Extract code from markdown with improved pattern matching
        print(f"Raw API response: {completion_text[:200]}...")  # Debug output

        # Try multiple patterns to extract code
        code_matches = re.findall(r'```(?:python)?\s*(.*?)```', completion_text, re.DOTALL)
        if not code_matches:
            # Try without language specifier
            code_matches = re.findall(r'```(.*?)```', completion_text, re.DOTALL)

        if code_matches:
            # Get the first code block and clean it
            extracted_code = code_matches[0].strip()
            print(f"Extracted code: {extracted_code[:100]}...")  # Debug output
            completion_text = extracted_code
        else:
            # If no code blocks found, check if the response itself looks like code
            completion_text = completion_text.strip()
            print(f"No code blocks found, using raw response: {completion_text[:100]}...")  # Debug output

            # If response is empty or very short, return an error
            if len(completion_text) < 10:
                return {"error": "API response too short or empty", "code": None}

        # Final validation - check if we have any meaningful code
        if not completion_text or len(completion_text.strip()) < 5:
            return {"error": "No meaningful code generated from API response", "code": None}

        return {"error": None, "code": completion_text}
    except (IndexError, AttributeError) as e:
        error_msg = f"Failed to parse response: {str(e)}"
        print(error_msg)
        return {"error": error_msg, "code": None}
    except Exception as e:
        error_msg = f"Stream processing failed: {str(e)}"
        print(error_msg)
        return {"error": error_msg, "code": None}

def test_openrouter_connection(api_key, base_url, model):
    """Test OpenRouter connection with a simple request"""
    try:
        print(f"Testing OpenRouter connection...")
        print(f"  API Key: {'*' * (len(api_key) - 4) + api_key[-4:] if len(api_key) > 4 else '****'}")
        print(f"  Base URL: {base_url}")
        print(f"  Model: {model}")

        client = OpenAI(api_key=api_key, base_url=base_url, timeout=30)

        # Simple test message
        test_messages = [
            {"role": "user", "content": "Say 'Hello' in Python code format"}
        ]

        response = client.chat.completions.create(
            model=model,
            messages=test_messages,
            stream=False,  # Use non-streaming for test
            max_tokens=50,
        )

        print(f"Test response: {response}")
        if response.choices and response.choices[0].message:
            content = response.choices[0].message.content
            print(f"Test content: {content}")
            return {"success": True, "content": content}
        else:
            return {"success": False, "error": "No content in response"}

    except Exception as e:
        print(f"Test failed: {e}")
        return {"success": False, "error": str(e)}

def split_area_to_text_editor(context):
    area = context.area
    for region in area.regions:
        if region.type == 'WINDOW':
            override = {'area': area, 'region': region}
            bpy.ops.screen.area_split(override, direction='VERTICAL', factor=0.5)
            break

    new_area = context.screen.areas[-1]
    new_area.type = 'TEXT_EDITOR'
    return new_area

# Multi-step Task Planning System
def is_complex_task(prompt):
    """
    Analyze if a prompt requires multi-step planning
    Returns True if the task is complex and needs to be broken down
    """
    complex_indicators = [
        # Multiple objects/actions
        'and', 've', 'ile', 'sonra', 'then', 'after',
        # Room/scene creation
        'oda', 'room', 'sahne', 'scene', 'ortam', 'environment',
        # Multiple furniture/objects
        'masa', 'table', 'sandalye', 'chair', 'dolap', 'cabinet', 'yatak', 'bed',
        # Material + object combinations
        'ahşap', 'wood', 'metal', 'cam', 'glass', 'taş', 'stone',
        # Complex descriptions
        'içinde', 'inside', 'üzerinde', 'on top', 'yanında', 'next to',
        # Multiple modifiers
        'büyük', 'küçük', 'yüksek', 'alçak', 'large', 'small', 'tall', 'short'
    ]

    prompt_lower = prompt.lower()
    indicator_count = sum(1 for indicator in complex_indicators if indicator in prompt_lower)

    # If multiple indicators or specific complex patterns
    if indicator_count >= 2:
        return True

    # Check for specific complex patterns
    complex_patterns = [
        r'.*oda.*masa.*sandalye',  # room with table and chair
        r'.*room.*table.*chair',
        r'.*içinde.*ve.*',  # inside ... and ...
        r'.*inside.*and.*',
        r'.*ile.*oluştur',  # create with ...
        r'.*with.*create'
    ]

    for pattern in complex_patterns:
        if re.search(pattern, prompt_lower):
            return True

    return False

def plan_complex_task(prompt, context, api_key, base_url=None, model=None):
    """
    Use AI to break down a complex task into smaller steps
    Returns a list of steps or None if planning fails
    """
    planning_prompt = f"""
Analyze this Blender task and break it down into clear, sequential steps. Each step should be a single, specific action that can be accomplished with one Blender Python command.

Task: "{prompt}"

Please respond with ONLY a JSON array of steps, where each step is a string describing a single action. For example:
["Create a cube and scale it to room size", "Create a wooden material", "Apply wooden material to the cube", "Create a table object", "Position table inside the room"]

Important:
- Each step should be ONE specific action
- Steps should be in logical order
- Use simple, clear language
- Focus on Blender-specific actions
- Do not include explanations, only the JSON array
"""

    try:
        client_kwargs = {"api_key": api_key, "timeout": 30}
        if base_url and base_url.strip():
            client_kwargs["base_url"] = base_url.strip()

        client = OpenAI(**client_kwargs)

        messages = [
            {"role": "system", "content": "You are a Blender expert who breaks down complex tasks into simple steps. Respond only with valid JSON arrays."},
            {"role": "user", "content": planning_prompt}
        ]

        selected_model = model if model and model.strip() else context.scene.blendpro_model

        response = client.chat.completions.create(
            model=selected_model,
            messages=messages,
            stream=False,
            max_tokens=800,
            temperature=0.3  # Lower temperature for more consistent planning
        )

        if response.choices and response.choices[0].message:
            content = response.choices[0].message.content.strip()

            # Try to extract JSON from the response
            try:
                # Look for JSON array in the response
                json_match = re.search(r'\[.*\]', content, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                    steps = json.loads(json_str)

                    if isinstance(steps, list) and len(steps) > 1:
                        print(f"Task planning successful: {len(steps)} steps generated")
                        return steps

            except json.JSONDecodeError as e:
                print(f"JSON parsing failed: {e}")

        print("Task planning failed - could not generate valid steps")
        return None

    except Exception as e:
        print(f"Task planning error: {e}")
        return None

def is_question_response(response_text):
    """
    Analyze if the AI response is asking a question rather than providing code
    """
    if not response_text:
        return False

    # Question indicators
    question_indicators = [
        '?', 'soru', 'question', 'hangi', 'which', 'ne kadar', 'how much',
        'nasıl', 'how', 'nerede', 'where', 'ne zaman', 'when',
        'istersiniz', 'would you like', 'istiyorsunuz', 'do you want',
        'belirtir misiniz', 'could you specify', 'açıklayabilir', 'can you explain'
    ]

    response_lower = response_text.lower()

    # Check for question indicators
    for indicator in question_indicators:
        if indicator in response_lower:
            return True

    # Check if response contains very little or no code
    code_patterns = [
        r'bpy\.',
        r'import\s+bpy',
        r'def\s+\w+',
        r'for\s+\w+\s+in',
        r'if\s+.*:',
        r'=\s*bpy\.'
    ]

    code_count = sum(1 for pattern in code_patterns if re.search(pattern, response_text))

    # If response has question indicators and little code, it's likely a question
    if any(indicator in response_lower for indicator in question_indicators) and code_count < 2:
        return True

    return False

def execute_step_by_step(steps, context, system_prompt, api_key, base_url=None, model=None):
    """
    Execute a list of steps one by one, checking scene state between steps
    Returns a list of results for each step
    """
    results = []

    for i, step in enumerate(steps):
        print(f"Executing step {i+1}/{len(steps)}: {step}")

        try:
            # Generate code for this specific step
            step_result = generate_blender_code(
                step,
                [], # Empty chat history for individual steps
                context,
                system_prompt,
                api_key,
                base_url,
                model,
                timeout=30,  # Shorter timeout for individual steps
                temperature=0.5,  # More focused for individual steps
                max_tokens=800   # Less tokens needed for single steps
            )

            if step_result.get('error'):
                print(f"Step {i+1} failed: {step_result['error']}")
                results.append({
                    'step': step,
                    'success': False,
                    'error': step_result['error'],
                    'code': None
                })
                # Continue with next step even if one fails
                continue

            code = step_result.get('code')
            if code and code.strip():
                results.append({
                    'step': step,
                    'success': True,
                    'error': None,
                    'code': code
                })
                print(f"Step {i+1} completed successfully")
            else:
                results.append({
                    'step': step,
                    'success': False,
                    'error': 'No code generated',
                    'code': None
                })
                print(f"Step {i+1} failed: No code generated")

            # Small delay between steps to prevent overwhelming the API
            time.sleep(0.5)

        except Exception as e:
            print(f"Step {i+1} exception: {e}")
            results.append({
                'step': step,
                'success': False,
                'error': str(e),
                'code': None
            })

    return results

def combine_step_results(results):
    """
    Combine successful step results into a single code block
    """
    successful_codes = []
    failed_steps = []

    for result in results:
        if result['success'] and result['code']:
            successful_codes.append(f"# Step: {result['step']}")
            successful_codes.append(result['code'])
            successful_codes.append("")  # Empty line between steps
        else:
            failed_steps.append(f"# FAILED: {result['step']} - {result['error']}")

    combined_code = "\n".join(successful_codes)

    if failed_steps:
        combined_code += "\n# FAILED STEPS:\n" + "\n".join(failed_steps)

    return combined_code

def generate_enhanced_blender_code(prompt, chat_history, context, system_prompt, api_key, base_url=None, model=None, timeout=60, temperature=0.7, max_tokens=1500, top_p=1.0):
    """
    Enhanced version of generate_blender_code that supports multi-step planning and question handling
    """

    # First, check if this is a complex task that needs planning
    if is_complex_task(prompt):
        print(f"Complex task detected: {prompt}")

        # Try to plan the task
        steps = plan_complex_task(prompt, context, api_key, base_url, model)

        if steps and len(steps) > 1:
            print(f"Task planned into {len(steps)} steps")

            # Execute steps one by one
            step_results = execute_step_by_step(steps, context, system_prompt, api_key, base_url, model)

            # Combine results
            combined_code = combine_step_results(step_results)

            if combined_code.strip():
                return {"error": None, "code": combined_code, "is_multi_step": True, "steps": steps}
            else:
                # If multi-step failed, fall back to single request
                print("Multi-step execution failed, falling back to single request")
        else:
            print("Task planning failed, using single request")

    # Use original single-request approach
    result = generate_blender_code(prompt, chat_history, context, system_prompt, api_key, base_url, model, timeout, temperature, max_tokens, top_p)

    # Check if the response is a question
    if result.get('code'):
        if is_question_response(result['code']):
            return {"error": None, "code": None, "question": result['code'], "is_question": True}

    # Add metadata to indicate this was a single-step result
    if result.get('code'):
        result['is_multi_step'] = False
        result['is_question'] = False

    return result